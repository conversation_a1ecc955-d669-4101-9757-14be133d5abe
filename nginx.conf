
#user  nobody;
worker_processes 1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;
events {
  worker_connections 1024;
}


http {
  include mime.types;
  default_type application/octet-stream;
  # server {
  #   listen 443 ssl;
  #   server_name api.finetabs.com;

  #   ssl_certificate /cer/api.finetabs.com.pem; # 证书文件路径
  #   ssl_certificate_key /cer/api.finetabs.com.key; # 私钥文件路径

  #   ssl_session_cache shared:SSL:1m;
  #   ssl_session_timeout 10m;
  #   ssl_ciphers HIGH:!aNULL:!MD5;
  #   ssl_prefer_server_ciphers on;
  #   charset utf-8;
  #   location / {
  #     proxy_pass http://localhost:8091/;
  #     proxy_http_version 1.1;
  #     proxy_set_header Upgrade $http_upgrade;
  #     proxy_set_header Connection keep-alive;
  #     proxy_set_header Host $host;
  #     proxy_cache_bypass $http_upgrade;
  #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  #     proxy_set_header X-Forwarded-Proto $scheme;
  #   }
  #   location /manage/ {
  #     proxy_pass http://localhost:8091/manage/;
  #   }
  #   location /api/ {
  #     proxy_pass http://localhost:8091/api/;
  #     proxy_max_temp_file_size 15M;
  #     proxy_temp_file_write_size 15M;
  #   }
  # }
  # 设置最大上传文件大小为50M
  client_max_body_size 20M;

  # 设置客户端请求读取超时时间
  client_body_timeout 120s;

  #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
  #                  '$status $body_bytes_sent "$http_referer" '
  #                  '"$http_user_agent" "$http_x_forwarded_for"';

  #access_log  logs/access.log  main;
  sendfile on;
  #tcp_nopush     on;

  #keepalive_timeout  0;
  keepalive_timeout 65;

  gzip on;
  # root /dist/;

  # server {
  #   listen 8090;
  #   server_name localhost;

  #   charset utf-8;
  #   #charset koi8-r;
  #   #access_log  logs/host.access.log  main;
  #   location / {
  #     root dist;
  #     try_files $uri $uri/ /index.html;
  #     index index.html;
  #   }
  #   location /api/ {
  #     proxy_pass http://127.0.0.1:8091/api/;
  #   }
  # }
  # another virtual host using mix of IP-, name-, and port-based configuration
  #
  server {
    listen 80;
    server_name www.finetabs.com;
    return 301 https://$host$request_uri;
  }
  server {
    listen 443 ssl;
    server_name www.finetabs.com;
    ssl_certificate /cer/www.finetabs.com.pem; # 证书文件路径
    ssl_certificate_key /cer/www.finetabs.com.key; # 私钥文件路径
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 10m;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    charset utf-8;
    #access_log  logs/host.access.log  main;
    location / {
      root www;
      try_files $uri $uri/ /index.html;
      index index.html;
    }
  }

  server {
    listen 80;
    server_name finetabs.com;
    return 301 https://$host$request_uri;
  }
  server {
    listen 443 ssl;
    server_name finetabs.com;
    ssl_certificate /cer/www.finetabs.com.pem; # 证书文件路径
    ssl_certificate_key /cer/www.finetabs.com.key; # 私钥文件路径
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 10m;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    charset utf-8;
    #access_log  logs/host.access.log  main;
    location / {
      root www;
      try_files $uri $uri/ /index.html;
      index index.html;
    }
  }

  server {
    listen 80;
    server_name v.finetabs.com;
    return 301 https://$host$request_uri;
  }
  server {
    listen 443 ssl;
    server_name v.finetabs.com;
    ssl_certificate /cer/v.finetabs.com.pem; # 证书文件路径
    ssl_certificate_key /cer/v.finetabs.com.key; # 私钥文件路径
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 10m;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    charset utf-8;
    #access_log  logs/host.access.log  main;
    location / {
      root dist;
      try_files $uri $uri/ /index.html;
      index index.html;
    }
    location /api/ {
      proxy_pass https://127.0.0.1:8091/api/;
    }
  }

  server {
    listen 8099;
    #server_name vvv.finetabs.com;
    charset utf-8;
    #access_log  logs/host.access.log  main;
    location / {
      root dist;
      try_files $uri $uri/ /index.html;
      index index.html;
    }
    location /api/ {
      add_header Referrer-Policy "no-referrer-when-downgrade";
      add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
      add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
      add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
      proxy_pass https://127.0.0.1:8091/api/;
    }
  }

  server {
    listen 443 ssl;
    server_name paper.finetabs.com;
    charset utf-8;
    #charset koi8-r;
    ssl_certificate /cer/paper.finetabs.com.pem; # 证书文件路径
    ssl_certificate_key /cer/paper.finetabs.com.key; # 私钥文件路径
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 10m;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    #access_log  logs/host.access.log  main;
    location / {
      root paper;
      try_files $uri $uri/ /index.html;
      index index.html;
    }
  }


  server {
    listen 80;
    server_name paper.finetabs.com;
    charset utf-8;
    #charset koi8-r;
    #access_log  logs/host.access.log  main;
    location / {
      root paper;
      try_files $uri $uri/ /index.html;
      index index.html;
    }
  }

  server {
    listen 443 ssl;
    server_name paperapi.finetabs.com;
    charset utf-8;
    #charset koi8-r;
    ssl_certificate /cer/paperapi.finetabs.com.pem; # 证书文件路径
    ssl_certificate_key /cer/paperapi.finetabs.com.key; # 私钥文件路径
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 10m;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    #access_log  logs/host.access.log  main;
    location / {
      proxy_pass http://127.0.0.1:9082/; # 假设IIS运行在8201端口
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection keep-alive;
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
    location /api/ {
      proxy_pass http://127.0.0.1:9082/api/;
      proxy_max_temp_file_size 15M;
      proxy_temp_file_write_size 15M;
    }
  }

  server {
    listen 443 ssl;
    server_name paperapix.finetabs.com;
    charset utf-8;
    #charset koi8-r;
    ssl_certificate /cer/paperapix.finetabs.com.pem; # 证书文件路径
    ssl_certificate_key /cer/paperapix.finetabs.com.key; # 私钥文件路径
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 10m;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    location / {
      proxy_pass https://*************:4433/; # 假设IIS运行在8201端口
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection keep-alive;
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
    location /api/ {
      proxy_pass https://*************:4433/api/;
      proxy_max_temp_file_size 15M;
      proxy_temp_file_write_size 15M;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection keep-alive;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
  }


  server {
    listen 80;
    server_name paperapi.finetabs.com;
    location / {
      proxy_pass http://127.0.0.1:9082/; # 假设IIS运行在8201端口
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection keep-alive;
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
    location /api/ {
      proxy_pass http://127.0.0.1:9082/api/;
      proxy_max_temp_file_size 15M;
      proxy_temp_file_write_size 15M;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
  }

  server {
    listen 80;
    server_name paperapix.finetabs.com;
    location / {
      proxy_pass http://*************:9081/; # 假设IIS运行在8201端口
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection keep-alive;
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
    location /api/ {
      proxy_pass http://*************:9081/api/;
      proxy_max_temp_file_size 15M;
      proxy_temp_file_write_size 15M;
    }
  }

  server {
    listen 80;
    server_name game.finetabs.com;
    location / {
      proxy_pass http://localhost:8092/; # 假设IIS运行在8080端口
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection keep-alive;
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
  }
  server {
    listen 443 ssl;
    server_name game.finetabs.com;
    ssl_certificate /cer/game.finetabs.com.pem; # 证书文件路径
    ssl_certificate_key /cer/game.finetabs.com.key; # 私钥文件路径
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 10m;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    location / {
      proxy_pass http://localhost:8092/; # 假设IIS运行在8080端口
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection keep-alive;
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
  }
  server {
    listen 8123;
    charset utf-8;
    location / {
      root bigscreen;
      try_files $uri $uri/ /index.html;
      index index.html;
    }
  }
  #server {
  #    listen       8000;
  #    listen       somename:8080;
  #    server_name  somename  alias  another.alias;
  #    location / {
  #        root   html;
  #        index  index.html index.htm;
  #    }
  #}
  # HTTPS server
  #
  #server {
  #    listen       443 ssl;
  #    server_name  localhost;
  #    ssl_certificate      cert.pem;
  #    ssl_certificate_key  cert.key;
  #    ssl_session_cache    shared:SSL:1m;
  #    ssl_session_timeout  5m;
  #    ssl_ciphers  HIGH:!aNULL:!MD5;
  #    ssl_prefer_server_ciphers  on;
  #    location / {
  #        root   html;
  #        index  index.html index.htm;
  #    }
  #}
}
