<template>
  <AquacultureHomepage
    v-if="currentPage === 'homepage'"
    @switch-to-dashboard2="switchToDashboard2"
  />
  <Dashboard2
    v-else-if="currentPage === 'dashboard2'"
    @switch-to-homepage="switchToHomepage"
  />
</template>

<script setup lang="ts">
import { ref } from "vue";
import AquacultureHomepage from "./pages/aquaculture-homepage.vue";
import Dashboard2 from "./pages/aquaculture-dashboard2.vue";
import autofit from "autofit.js";

// 当前页面状态
const currentPage = ref<"homepage" | "dashboard2">("homepage");

// 切换到 dashboard2
const switchToDashboard2 = () => {
  currentPage.value = "dashboard2";
};

// 切换到主页
const switchToHomepage = () => {
  currentPage.value = "homepage";
};

autofit.init();
</script>
